'use client';
import { IconBookmark, IconRefresh, IconPhotoBitcoin, IconMail, IconExternalLink, IconPhoneCall, IconNotes, IconCopy } from '@tabler/icons-react';
import { Avatar, Group, Text, Card, Image, ActionIcon, Modal, Tooltip, Anchor, Badge, Code, Stack } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import classes from './contact.module.css';
import { useState, useEffect } from 'react';
import { fetchData, updateData } from 'src/lib/supabase';
import { useSession } from "next-auth/react"
import { checkIfBookmarked, toggleBookmark, cleanupBookmarksForNonExistentContact } from 'src/lib/bookmark';
import { fetchDomain, fetchNFTowner, getImage, ImageData } from 'src/lib/common';
import { prepareContactData, getNotesData, hasNotesData } from 'src/lib/database-utils';
import { insertContactData } from 'src/components/Search';

interface UserInfo {
  website: string;
  profile: string;
  email: string;
  phone?: string;
  image?: string;
  description?: string;
  profile_email?: string;
  images?: ImageData;
  minted?: string;
  notes?: Record<string, string> | null;
}


export function ContactCard({ name }: { name: string }) {
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshLoading, setRefreshLoading] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [nftModalOpened, setNftModalOpened] = useState(false);
  const [avatarModalOpened, setAvatarModalOpened] = useState(false);
  const [notesModalOpened, setNotesModalOpened] = useState(false);
  const { data: session } = useSession();

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const { data } = await fetchData<UserInfo>('contact', {
          select: 'profile, email, image, profile_email, description, images, website, phone, minted, notes',
          filter: [{ column: 'name', value: name.toLowerCase() }],
          single: true
        });
        //console.log('User data from Supabase:', data);
        const userData = Array.isArray(data) ? data[0] : data;
        setUserInfo(userData);

        // Check if this contact is bookmarked
        if (session?.user?.email) {
          const bookmarked = await checkIfBookmarked(name.toLowerCase(), session.user.email);
          setIsBookmarked(bookmarked);
        }

        // If user is not found, clean up any existing bookmarks
        if (!userData) {
          await cleanupBookmarksForNonExistentContact(name.toLowerCase());
        }

      } catch (error) {
        console.error('Error fetching user data:', error);
        // Also clean up bookmarks on error (user likely doesn't exist)
        await cleanupBookmarksForNonExistentContact(name);
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, [name, session?.user?.email]);

  const handleBookmarkClick = async () => {
    const newBookmarkStatus = await toggleBookmark(
      name,
      isBookmarked,
      session?.user?.email
    );
    setIsBookmarked(newBookmarkStatus);
  };

  const handleRefreshClick = async () => {
    setRefreshLoading(true);
    try {
      // Fetch fresh data from external API
      const jsonData = await fetchDomain(name);

      if (jsonData.error) {
        notifications.show({
          title: 'Error',
          message: jsonData.error,
          color: 'red',
        });
        return;
      }

      // Prepare data for database
      const contact = prepareContactData(jsonData);
      // Ensure name is lowercase
      contact.name = contact.name.toLowerCase();

      // Update existing record instead of delete and insert
      const updateResult = await updateData(
        'contact',
        contact,
        { column: 'name', value: name.toLowerCase() }
      );
      console.log('Record updated:', updateResult);

      if (updateResult.error) {
        // If update fails (record might not exist), try to insert
        console.log('Update failed, attempting insert:', updateResult.error);
        const insertResult = await insertContactData(contact);

        if (insertResult.error) {
          notifications.show({
            title: 'Error',
            message: 'Failed to update record',
            color: 'red',
          });
          return;
        } else {
          console.log('Record inserted successfully:', insertResult);
        }
      }

      notifications.show({
        title: 'Success',
        message: 'Record updated successfully',
        color: 'green',
      });

      // Check NFT owner and update minted column
      try {
        const nftOwnerAddress = await fetchNFTowner(name);
        if (nftOwnerAddress) {
          // Update the minted column with the NFT owner address
          const mintedUpdateResult = await updateData(
            'contact',
            { minted: nftOwnerAddress },
            { column: 'name', value: name.toLowerCase() }
          );

          if (mintedUpdateResult.error) {
            console.error('Failed to update minted column:', mintedUpdateResult.error);
          } else {
            // Show separate notification about NFT status
            notifications.show({
              title: 'NFT Found',
              message: `This name is already minted on blockchain. Owner: ${nftOwnerAddress}`,
              color: 'blue',
            });
          }
        }
      } catch (nftError) {
        console.error('Error checking NFT owner:', nftError);
      }

      // Refresh the local state by fetching updated data
      const { data } = await fetchData<UserInfo>('contact', {
        select: 'profile, email, image, profile_email, description, images, website, phone, minted, notes',
        filter: [{ column: 'name', value: name.toLowerCase() }],
        single: true
      });
      const userData = Array.isArray(data) ? data[0] : data;
      setUserInfo(userData);

    } catch (error) {
      console.error('Update operation failed:', error);
      notifications.show({
        title: 'Error',
        message: 'Failed to update record',
        color: 'red',
      });
    } finally {
      setRefreshLoading(false);
    }
  };

  const handleNftClick = () => {
    setNftModalOpened(true);
  };

  const handleAvatarClick = () => {
    setAvatarModalOpened(true);
  };

  const handleNotesClick = () => {
    setNotesModalOpened(true);
  };

  const handleCopyNotes = async () => {
    if (userInfo?.notes) {
      try {
        const notesData = getNotesData(userInfo);
        const allNotes = Object.entries(notesData)
          .map(([title, content]) => `${title}:\n${content}`)
          .join('\n\n');

        await navigator.clipboard.writeText(allNotes);
        notifications.show({
          title: 'Copied!',
          message: 'All notes copied to clipboard',
          color: 'green',
        });
      } catch (error) {
        notifications.show({
          title: 'Error',
          message: 'Failed to copy notes',
          color: 'red',
        });
      }
    }
  };

  const handleCopyIndividualNote = async (title: string, content: string) => {
    try {
      // Only copy the content, not the title
      await navigator.clipboard.writeText(content);
      notifications.show({
        title: 'Copied!',
        message: `"${title}" note copied to clipboard`,
        color: 'green',
      });
    } catch (error) {
      notifications.show({
        title: 'Error',
        message: 'Failed to copy note',
        color: 'red',
      });
    }
  };

  // Check if user has any images
  const hasImages = () => {
    return !!(userInfo?.image && userInfo.image.trim() !== '');
  };

  // Check if user has notes
  const hasNotes = () => {
    return hasNotesData(userInfo || {});
  };

  if (loading) return <div>Loading...</div>;
  if (!userInfo) return <div>User not found : {name}</div>;
  return (
    <>
      <Card withBorder padding="lg" radius="md" className={classes.card}>


        <Card.Section
          h={150}
          style={{
            backgroundImage:
              `url(${getImage(userInfo.images, 2)})`,
          }}
        />
        <Avatar
          src={getImage(userInfo.images, 1)}
          size={80}
          radius={80}
          mx="auto"
          mt={-30}
          className={classes.avatar}
          onClick={handleAvatarClick}
          style={{ cursor: 'pointer' }}
        />


        <Text ta="center" fz="lg" fw={700} mt="sm"   variant="gradient" gradient={{ from: 'blue', to: 'cyan', deg: 90 }}>
          {userInfo.profile}
        </Text>
        <Text ta="center" fz="sm" c="dimmed">
          {userInfo.description}
        </Text>

        {/* Contact Information - Single line with responsive wrapping */}
        {(userInfo.email?.trim() || userInfo.website?.trim() || userInfo.phone?.trim()) && (
          <Group gap="md" justify="center" mt="sm" wrap="wrap">
            {/* Email */}
            {userInfo.email && userInfo.email.trim() !== '' && (
              <Group gap="xs" wrap="nowrap">
                <IconMail size={16} />
                <Anchor href={`mailto:${userInfo.email}`} size="sm">
                  {userInfo.email}
                </Anchor>
              </Group>
            )}

            {/* Website */}
            {userInfo.website && userInfo.website.trim() !== '' && (
              <Group gap="xs" wrap="nowrap">
                <IconExternalLink size={14} />
                <Anchor href={userInfo.website} target="_blank" rel="noopener noreferrer" size="sm">
                  {userInfo.website}
                </Anchor>
              </Group>
            )}

            {/* Phone */}
            {userInfo.phone && userInfo.phone.trim() !== '' && (
              <Group gap="xs" wrap="nowrap">
                <IconPhoneCall size={16} />
                <Anchor href={`tel:${userInfo.phone}`} size="sm">
                  {userInfo.phone}
                </Anchor>
              </Group>
            )}
          </Group>
        )}

        <Card.Section className={classes.footer}>
          <Group justify="space-between">
            <Group gap="xs" align="center">
              <Code color="var(--mantine-color-blue-light)">
                {name}
              </Code>
              {userInfo?.minted && userInfo.minted.trim() !== '' && (
                <Tooltip label="Blockchain verified" withArrow>
                  <Badge variant="filled" color="green" size="sm">
                    verified
                  </Badge>
                </Tooltip>
              )}
            </Group>
            <Group gap={0}>
              <Tooltip label={isBookmarked ? "Remove Bookmark" : "Add Bookmark"} withArrow>
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  onClick={handleBookmarkClick}
                  style={{ cursor: 'pointer' }}
                >
                  <IconBookmark
                    size={50}
                    color={isBookmarked ? "var(--mantine-color-yellow-6)" : "var(--mantine-color-gray-6)"}
                    stroke={1.5}
                    fill={isBookmarked ? "var(--mantine-color-yellow-6)" : "none"}
                  />
                </ActionIcon>
              </Tooltip>
              {hasNotes() && (
                <Tooltip label="View Notes" withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleNotesClick}
                    style={{ cursor: 'pointer' }}
                  >
                    <IconNotes
                      size={50}
                      color="var(--mantine-color-blue-6)"
                      stroke={1.5}
                    />
                  </ActionIcon>
                </Tooltip>
              )}
              {hasImages() && (
                <Tooltip label="View NFT Image" withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleNftClick}
                    style={{ cursor: 'pointer' }}
                  >
                    <IconPhotoBitcoin
                      size={50}
                      color="var(--mantine-color-green-6)"
                      stroke={1.5}
                    />
                  </ActionIcon>
                </Tooltip>
              )}
              {userInfo.image && (
                <Tooltip label="Refresh Contact Data" withArrow>
                  <ActionIcon
                    variant="subtle"
                    color="gray"
                    onClick={handleRefreshClick}
                    loading={refreshLoading}
                    style={{ cursor: 'pointer' }}
                  >
                    <IconRefresh size={20} color="var(--mantine-color-blue-6)" stroke={1.5} />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>
          </Group>
        </Card.Section>
      </Card>

      {/* NFT Image Modal */}
      <Modal
        opened={nftModalOpened}
        onClose={() => setNftModalOpened(false)}
        title={
          <Group gap="xs" align="center">
            <Text>NFT Image</Text>
            {userInfo?.minted && userInfo.minted.trim() !== '' && (
              <Tooltip label="Blockchain verified" withArrow>
                <Badge variant="filled" color="green" size="sm">
                  verified
                </Badge>
              </Tooltip>
            )}
          </Group>
        }
        size="lg"
        centered
      >
        <div style={{ textAlign: 'center' }}>
          <Image
            src={userInfo?.image}
            alt="NFT Image"
            style={{
              maxWidth: '100%',
              maxHeight: '400px',
              objectFit: 'contain'
            }}
          />
        </div>
      </Modal>

      {/* Avatar Image Modal */}
      <Modal
        opened={avatarModalOpened}
        onClose={() => setAvatarModalOpened(false)}
        title={
          <Group gap="xs" align="center">
            <Text>{userInfo?.profile}</Text>
            {userInfo?.minted && userInfo.minted.trim() !== '' && (
              <Tooltip label="Blockchain verified" withArrow>
                <Badge variant="filled" color="green" size="sm">
                  verified
                </Badge>
              </Tooltip>
            )}
          </Group>
        }
        size="lg"
        centered
      >
        <div style={{ textAlign: 'center' }}>
          <Image
            src={getImage(userInfo?.images, 1)}
            alt="Profile Image"
            style={{
              maxWidth: '100%',
              maxHeight: '500px',
              objectFit: 'contain'
            }}
          />
        </div>
      </Modal>

      {/* Notes Modal */}
      <Modal
        opened={notesModalOpened}
        onClose={() => setNotesModalOpened(false)}
        title={
          <Group gap="xs" align="center" justify="space-between" style={{ width: '100%' }}>
            <Group gap="xs" align="center">
              <IconNotes size={20} />
              <Text>Notes - {userInfo?.profile}</Text>
            </Group>
            <Tooltip label="Copy Notes" withArrow>
              <ActionIcon
                variant="subtle"
                color="blue"
                onClick={handleCopyNotes}
                style={{ cursor: 'pointer' }}
              >
                <IconCopy size={16} />
              </ActionIcon>
            </Tooltip>
          </Group>
        }
        size="md"
        centered
      >
        <Card withBorder padding="lg" radius="md" style={{ minHeight: '100px' }}>
          {(() => {
            const notesData = getNotesData(userInfo || {});
            const noteEntries = Object.entries(notesData);

            if (noteEntries.length === 0) {
              return (
                <Text size="sm" c="dimmed" ta="center">
                  No notes available
                </Text>
              );
            }

            return (
              <Stack gap="md">
                {noteEntries.map(([title, content], index) => (
                  <div key={index}>
                    <Group gap="xs" align="center" mb="xs">
                      <Text fw={600} size="sm" c="blue">
                        {title}
                      </Text>
                      <Tooltip label={`Copy "${title}" note`} withArrow>
                        <ActionIcon
                          variant="subtle"
                          color="gray"
                          size="sm"
                          onClick={() => handleCopyIndividualNote(title, content)}
                          style={{ cursor: 'pointer' }}
                        >
                          <IconCopy size={14} />
                        </ActionIcon>
                      </Tooltip>
                    </Group>
                    <Text
                      size="sm"
                      style={{
                        whiteSpace: 'pre-wrap',
                        lineHeight: '1.6',
                        fontFamily: 'monospace',
                        backgroundColor: 'light-dark(var(--mantine-color-gray-0), var(--mantine-color-dark-6))',
                        color: 'light-dark(var(--mantine-color-dark-9), var(--mantine-color-gray-0))',
                        padding: '8px',
                        borderRadius: '4px',
                        border: '1px solid light-dark(var(--mantine-color-gray-3), var(--mantine-color-dark-4))'
                      }}
                    >
                      {content}
                    </Text>
                  </div>
                ))}
              </Stack>
            );
          })()}
        </Card>
      </Modal>
    </>
  );
}